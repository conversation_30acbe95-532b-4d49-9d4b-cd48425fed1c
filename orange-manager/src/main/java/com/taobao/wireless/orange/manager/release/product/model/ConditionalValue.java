package com.taobao.wireless.orange.manager.release.product.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionalValue {
    /**
     * 关联条件标识符，指示该值应用的条件
     * 必填字段
     * 示例值: "Oppo2311"
     */
    private String conditionId;

    /**
     * 在满足某个条件时应用的参数值
     * 必填字段
     * 示例值: {"limit":12}
     */
    private Object value;
}
