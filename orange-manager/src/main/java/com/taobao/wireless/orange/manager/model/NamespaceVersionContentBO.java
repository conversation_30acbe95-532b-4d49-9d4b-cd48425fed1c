package com.taobao.wireless.orange.manager.model;

import com.taobao.wireless.orange.manager.release.product.model.Expression;
import com.taobao.wireless.orange.manager.release.product.model.Parameter;
import lombok.Data;

import java.util.List;

@Data
public class NamespaceVersionContentBO {

    /**
     * 条件数组，在特定条件下应用配置参数
     * 必填字段
     */
    private List<Condition> conditions;

    /**
     * 参数数组，定义具体的配置项和其条件值
     * 必填字段
     */
    private List<Parameter> parameters;

    @Data
    public static class Condition {
        private String conditionId;
        private String name;
        private Expression expression;
        private String releaseVersion;
    }
}