package com.taobao.wireless.orange.service.scheduler;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.wireless.orange.manager.release.product.generator.ProbeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ProbeJob extends JavaProcessor {
    @Autowired
    private ProbeManager probeManager;

    @Override
    public ProcessResult process(JobContext context) {
        try {
            probeManager.generate();
            log.info("Probe generate success");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("Probe generate error", e);
            return new ProcessResult(false);
        }
    }
}
