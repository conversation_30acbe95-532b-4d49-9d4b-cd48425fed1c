package com.taobao.wireless.orange.manager.release.workflow.impl;

import com.taobao.wireless.orange.common.constant.enums.OperationType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.constant.enums.VerifyStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.manager.TaskManager;
import com.taobao.wireless.orange.manager.model.VerifyReplyBO;
import com.taobao.wireless.orange.manager.release.workflow.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.workflow.OperationContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 验证操作策略实现
 */
@Component
public class VerifyStrategy extends AbstractOperationTemplate {

    @Autowired
    private TaskManager taskManager;

    @Override
    public OperationType getOperationType() {
        return OperationType.VERIFY_REPLY;
    }

    @Override
    public void validateStatus(OperationContext context) {
        var releaseOrder = context.getReleaseOrder();

        if (!ReleaseOrderStatus.WAIT_VERIFY.equals(releaseOrder.getStatus())) {
            throw new CommonException(ExceptionEnum.RELEASE_ORDER_STATUS_INVALID);
        }
    }

    @Override
    public void validatePermission(OperationContext context) {
        var namespace = context.getNamespace();
        String workerId = ThreadContextUtil.getWorkerId();

        if (StringUtils.isBlank(workerId) || !namespace.getTesters().contains(workerId)) {
            throw new CommonException(ExceptionEnum.NO_PERMISSION);
        }
    }

    @Override
    public void validateParameters(OperationContext context) {
    }

    @Override
    public void executeOperation(OperationContext context) {

    }

    @Override
    public ReleaseOrderStatus getTargetStatus(OperationContext context) {
        VerifyReplyBO verifyReplyBO = (VerifyReplyBO) context.getAdditionalData();
        return VerifyStatus.PASS.equals(verifyReplyBO.getVerifyStatus()) ? ReleaseOrderStatus.VERIFY_PASS : ReleaseOrderStatus.VERIFY_REFUSE;
    }

    @Override
    @Async("taskExecutor")
    protected void handleTask(OperationContext context) {
        taskManager.completeVerifyTask(context.getReleaseVersion());

        VerifyReplyBO verifyReplyBO = (VerifyReplyBO) context.getAdditionalData();
        if (VerifyStatus.PASS.equals(verifyReplyBO.getVerifyStatus())) {
            ONamespaceDO namespace = context.getNamespace();
            String releaseVersion = context.getReleaseVersion();

            if (namespace == null || CollectionUtils.isEmpty(namespace.getOwners())) {
                return;
            }

            // 创建验收任务并分派给测试人员
            String description = String.format("%s-%s-版本 %s 验收通过待发布",
                    namespace.getAppKey(), namespace.getName(), releaseVersion);

            taskManager.createReleaseTask(context.getReleaseVersion(), namespace);
        }
    }
}