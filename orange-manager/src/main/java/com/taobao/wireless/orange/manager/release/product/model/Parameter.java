package com.taobao.wireless.orange.manager.release.product.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Parameter {
    /**
     * 参数标识符，描述配置项的唯一名称
     * 必填字段
     * 示例值: "user_config"
     */
    private String key;

    /**
     * 参数版本号
     * 必填字段
     * 示例值: "0212025030712133421"
     */
    private long version;

    /**
     * 参数值类型，描述参数的值如何存储和解释
     * 必填字段
     * 示例值: "json"
     */
    private String valueType;

    /**
     * 参数默认值，不满足任何条件时使用的值
     * 必填字段
     * 示例值: {"limit":1}
     */
    private Object defaultValue;

    /**
     * 条件值数组，根据条件应用不同的配置
     * 必填字段
     */
    private List<ConditionalValue> conditionalValues;
}
