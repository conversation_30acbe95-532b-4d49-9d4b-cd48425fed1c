package com.taobao.wireless.orange.manager.release.product.generator.config;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.common.constant.enums.ConfigType;
import com.taobao.wireless.orange.common.constant.enums.ReleaseOrderStatus;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO;
import com.taobao.wireless.orange.manager.release.product.model.Condition;
import com.taobao.wireless.orange.manager.release.product.model.Expression;
import com.taobao.wireless.orange.manager.release.product.model.GrayConfig;
import com.taobao.wireless.orange.manager.release.product.model.Parameter;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 灰度配置生成器
 * 负责生成包含灰度发布单信息的配置，用于客户端灰度控制
 *
 * <AUTHOR>
 */
@Component
public class GrayConfigGenerator extends AbstractConfigGenerator<GrayConfig> {
    /**
     * 获取灰度发布版本列表
     * 查询所有未完成且已设置灰度百分比的发布单版本
     *
     * @param namespaceId 命名空间ID
     * @param baseVersion 基础版本号（灰度场景下不使用）
     * @return 发布版本列表
     */
    @Override
    protected List<String> getReleaseVersions(String namespaceId, String baseVersion) {
        return releaseOrderDAO.lambdaQuery()
                .eq(OReleaseOrderDO::getNamespaceId, namespaceId)
                .in(OReleaseOrderDO::getStatus, ReleaseOrderStatus.getNotFinishedStatuses())
                .isNotNull(OReleaseOrderDO::getPercent)
                .list()
                .stream()
                .map(OReleaseOrderDO::getReleaseVersion)
                .collect(Collectors.toList());
    }

    /**
     * 获取灰度参数版本列表
     * 查询指定发布版本下状态为INIT的参数版本
     *
     * @param namespaceId     命名空间ID
     * @param releaseVersions 发布版本列表
     * @return 参数版本列表
     */
    @Override
    protected List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions) {
        if (CollectionUtils.isEmpty(releaseVersions)) {
            return Collections.emptyList();
        }
        return parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getNamespaceId, namespaceId)
                .in(OParameterVersionDO::getReleaseVersion, releaseVersions)
                .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                .list();
    }

    /**
     * 构建灰度配置结果
     * 将条件映射和发布单信息组装成最终的灰度配置对象
     *
     * @param namespace           命名空间信息
     * @param onlineParameters    待更新参数列表
     * @param offlineParameterDOs 待下线参数列表
     * @param conditionDOs        条件列表
     * @return 灰度配置对象
     */
    @Override
    protected GrayConfig buildResult(NamespaceIdNameRecord namespace,
                                     List<Parameter> onlineParameters,
                                     List<OParameterVersionDO> offlineParameterDOs,
                                     Collection<OConditionVersionDO> conditionDOs) {
        List<GrayConfig.ReleaseOrder> orders = buildGrayReleaseOrders(namespace.namespaceId(), onlineParameters, offlineParameterDOs);
        List<Condition> conditions = buildConditionList(conditionDOs);

        return GrayConfig.builder()
                .schemaVersion("1.0")
                .namespace(namespace.name())
                .strategy(ConfigStrategy.FULL)
                .type(ConfigType.GRAY)
                .conditions(conditions)
                .orders(orders)
                .build();
    }

    /**
     * 构建条件列表
     * 将条件映射转换为排序后的条件列表，便于客户端检索
     *
     * @param conditions 条件列表
     * @return 排序后的条件列表
     */
    private List<Condition> buildConditionList(Collection<OConditionVersionDO> conditions) {
        return CollectionUtils.isEmpty(conditions) ? null :
                conditions.stream()
                        .map(this::convertToCondition)
                        .sorted(Comparator.comparing(Condition::getId))
                        .collect(Collectors.toList());
    }

    /**
     * 将条件版本DO转换为条件对象
     *
     * @param conditionVersion 条件版本DO
     * @return 条件对象
     */
    private Condition convertToCondition(OConditionVersionDO conditionVersion) {
        return Condition.builder()
                .id(conditionVersion.getConditionId())
                .expression(JSON.parse(conditionVersion.getExpression(), Expression.class))
                .build();
    }

    /**
     * 构建灰度发布单列表
     * 为每个发布版本构建包含参数和下线参数的发布单对象
     *
     * @param namespaceId 命名空间ID
     * @return 灰度发布单列表
     */
    private List<GrayConfig.ReleaseOrder> buildGrayReleaseOrders(String namespaceId,
                                                                 List<Parameter> onlineParameters,
                                                                 List<OParameterVersionDO> offlineParameterDOs) {

        var releaseVersion2OnlineParameters = onlineParameters.stream().collect(Collectors.groupingBy(Parameter::getVersion));
        var releaseVersion2OfflineParameters = offlineParameterDOs.stream().collect(Collectors.groupingBy(p -> Long.parseLong(p.getReleaseVersion())));

        Set<Long> releaseVersions = new HashSet<>(releaseVersion2OnlineParameters.keySet());
        releaseVersions.addAll(releaseVersion2OfflineParameters.keySet());

        return releaseVersions.stream()
                .map(version -> buildSingleReleaseOrder(namespaceId,
                        version,
                        releaseVersion2OnlineParameters.get(version),
                        releaseVersion2OfflineParameters.get(version)))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个发布单对象
     *
     * @param namespaceId         命名空间ID
     * @param version             发布版本
     * @param onlineParameter     在线参数版本列表
     * @param offlineParameterDOs 下线参数版本列表
     * @return 发布单对象，如果没有参数则返回null
     */
    private GrayConfig.ReleaseOrder buildSingleReleaseOrder(String namespaceId,
                                                            long version,
                                                            List<Parameter> onlineParameter,
                                                            List<OParameterVersionDO> offlineParameterDOs
    ) {
        List<String> offlineParameterKeys = CollectionUtils.isEmpty(offlineParameterDOs) ? null : offlineParameterDOs.stream()
                .map(OParameterVersionDO::getParameterKey)
                .toList();

        return GrayConfig.ReleaseOrder.builder()
                .version(version)
                .parameters(onlineParameter)
                .offlineParameters(offlineParameterKeys)
                .build();
    }

    /**
     * 获取版本状态列表
     * 灰度配置需要查询INIT和RELEASED状态的版本
     *
     * @return 版本状态列表
     */
    @Override
    protected List<VersionStatus> getVersionStatuses() {
        return Arrays.asList(VersionStatus.RELEASED, VersionStatus.INIT);
    }
}
