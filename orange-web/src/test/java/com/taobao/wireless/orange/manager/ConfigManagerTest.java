package com.taobao.wireless.orange.manager;

import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.constant.enums.ConfigStrategy;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceDAO;
import com.taobao.wireless.orange.manager.release.product.generator.config.ConfigManager;
import com.taobao.wireless.orange.manager.release.product.generator.config.FullReleaseConfigGenerator;
import com.taobao.wireless.orange.manager.release.product.generator.config.GrayConfigGenerator;
import com.taobao.wireless.orange.manager.release.product.generator.config.IncrementalReleaseConfigGenerator;
import com.taobao.wireless.orange.manager.release.product.model.GrayConfig;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.manager.release.product.model.ReleaseConfig;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;


public class ConfigManagerTest extends BaseIntegrationTest {
    @Autowired
    private ConfigManager configManager;

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private IncrementalReleaseConfigGenerator incrementalReleaseConfigGenerator;
    @Autowired
    private GrayConfigGenerator grayConfigGenerator;
    @Autowired
    private ONamespaceDAO namespaceDAO;

    private String namespaceId = "cd3c20aa956e49bfb1909d0c80f3bda3";

    @Test
    public void generateIncrementalReleaseConfig() {
        NamespaceIdNameRecord namespace = Optional.ofNullable(this.namespaceDAO.getByNamespaceId(namespaceId)).map(n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName())).orElseThrow(() -> new RuntimeException("namespace not exist"));

        ReleaseConfig releaseConfig1 = this.incrementalReleaseConfigGenerator.generate(namespace, "0");
        ReleaseConfig releaseConfig2 = this.fullReleaseConfigGenerator.generate(namespace, "0");
        Assert.assertEquals(releaseConfig1.getStrategy(), ConfigStrategy.INCREMENTAL);
        releaseConfig1.setStrategy(ConfigStrategy.FULL);
        Assert.assertEquals(JSON.toJSONString(releaseConfig1), JSON.toJSONString(releaseConfig2));
        System.out.printf(JSON.toJSONString(releaseConfig1));
    }

    @Test
    public void generateGrayConfig() {
        NamespaceIdNameRecord namespace = Optional.ofNullable(this.namespaceDAO.getByNamespaceId(namespaceId)).map(n -> new NamespaceIdNameRecord(n.getNamespaceId(), n.getName())).orElseThrow(() -> new RuntimeException("namespace not exist"));
        GrayConfig grayConfig = this.grayConfigGenerator.generate(namespace, "0");
        System.out.println(JSON.toJSONString(grayConfig));
    }
}
