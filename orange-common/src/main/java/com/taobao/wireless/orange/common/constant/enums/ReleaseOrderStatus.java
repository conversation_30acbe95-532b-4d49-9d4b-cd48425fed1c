package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ReleaseOrderStatus {
    // 初始
    INIT("INIT"),
    // beta 验证通过
    BETA_PASS("BETA_PASS"),
    // 发布申请通过
    APPLY_PASS("APPLY_PASS"),
    // 定量灰度中
    IN_GRAY("IN_GRAY"),
    // 百分比发布中
    IN_RATIO_GRAY("IN_RATIO_GRAY"),
    // 已提交，待验证
    WAIT_VERIFY("WAIT_VERIFY"),
    // 验证通过
    VERIFY_PASS("VERIFY_PASS"),
    // 验证被拒绝
    VERIFY_REFUSE("VERIFY_REFUSE"),
    // 已发布
    RELEASED("RELEASED"),
    // 取消
    CANCELED("CANCELED"),
    ;

    private final String value;

    public boolean isFinished() {
        return this == CANCELED || this == RELEASED;
    }

    // 获取未结束的状态列表
    public static List<ReleaseOrderStatus> getNotFinishedStatuses() {
        return Arrays.stream(ReleaseOrderStatus.values()).filter(status -> !status.isFinished()).collect(Collectors.toList());
    }
}
