package com.taobao.wireless.orange.manager;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.taobao.wireless.orange.BaseIntegrationTest;
import com.taobao.wireless.orange.common.model.proto.IndexProto;
import com.taobao.wireless.orange.dal.enhanced.dao.OResourceDAO;
import com.taobao.wireless.orange.external.config.SwitchConfig;
import com.taobao.wireless.orange.manager.release.product.generator.ProbeManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Base64;


public class ProbeManagerTest extends BaseIntegrationTest {
    @Autowired
    private ProbeManager probeManager;

    @Autowired
    private OResourceDAO resourceDAO;

    @Test
    public void generate() {
        SwitchConfig.protocolType = "protobuf";
        var newProbes = probeManager.generate();
        System.out.println(newProbes);
    }

    @Test
    public void testProbeContent() throws InvalidProtocolBufferException {
        var resource = resourceDAO.getById(595);
        byte[] byteArray = Base64.getDecoder().decode(resource.getData());
        MessageOrBuilder proto = IndexProto.parseFrom(byteArray);
        String jsonStr = JsonFormat.printer()
                .includingDefaultValueFields()
                .print(proto);
        System.out.println(jsonStr);
    }
}
