package com.taobao.wireless.orange.manager.release.product.generator.config;

import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.ParameterValueType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO;
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO;
import com.taobao.wireless.orange.manager.release.product.model.ConditionalValue;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.manager.release.product.model.Parameter;
import com.taobao.wireless.orange.manager.model.ParameterGroup;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.CONDITION_SEPARATOR;
import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;

/**
 * 配置生成器抽象类，使用模板方法模式实现配置生成的通用流程
 *
 * @param <T> 生成的配置类型
 */
public abstract class AbstractConfigGenerator<T> {
    @Autowired
    protected OConditionVersionDAO conditionVersionDAO;

    @Autowired
    protected OParameterVersionDAO parameterVersionDAO;

    @Autowired
    protected OParameterConditionVersionDAO parameterConditionVersionDAO;

    @Autowired
    protected ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    protected ONamespaceDAO namespaceDAO;

    @Autowired
    protected OReleaseOrderDAO releaseOrderDAO;

    /**
     * 模板方法：定义配置生成的核心流程
     *
     * @param namespace   命名空间
     * @param baseVersion 基础版本号（可选）
     * @return 生成的配置对象
     */
    public final T generate(NamespaceIdNameRecord namespace, String baseVersion) {
        // 获取发布版本列表
        List<String> releaseVersions = getReleaseVersions(namespace.namespaceId(), baseVersion);

        // 获取参数版本列表
        List<OParameterVersionDO> parameterVersions = getParameterVersions(namespace.namespaceId(), releaseVersions);
        if (CollectionUtils.isEmpty(parameterVersions)) {
            return null;
        }

        // 分组参数（上线/下线）
        ParameterGroup group = groupParameters(parameterVersions);

        // 构建条件映射
        Map<String, OConditionVersionDO> conditionId2Condition = buildConditionMap(group.onlineParameters());

        // 构建参数列表
        List<Parameter> onlineParameters = buildParameters(group.onlineParameters(), conditionId2Condition);

        // 构建最终结果
        return buildResult(namespace, onlineParameters, group.offlineParameters(), conditionId2Condition.values());
    }

    /**
     * 获取发布版本列表
     *
     * @param baseVersion 基础版本号
     * @return 发布版本列表
     */
    protected abstract List<String> getReleaseVersions(String namespaceId, String baseVersion);

    /**
     * 获取参数版本列表
     *
     * @param releaseVersions 发布版本列表
     * @return 参数版本列表
     */
    protected abstract List<OParameterVersionDO> getParameterVersions(String namespaceId, List<String> releaseVersions);

    /**
     * 将参数按照变更类型分组
     *
     * @param parameterVersions 参数版本列表
     * @return 参数版本分组（上线参数和下线参数）
     */
    private ParameterGroup groupParameters(List<OParameterVersionDO> parameterVersions) {
        // 按照参数变更类型分组：true=下线参数，false=上线参数
        Map<Boolean, List<OParameterVersionDO>> parameterMap = parameterVersions
                .stream()
                .collect(Collectors.groupingBy(i -> ChangeType.DELETE.equals(i.getChangeType())));

        // 提取下线参数列表
        List<OParameterVersionDO> offlineParameters = Optional.ofNullable(parameterMap.get(true))
                .orElse(Collections.emptyList());

        // 获取上线参数列表
        List<OParameterVersionDO> onlineParameters = Optional.ofNullable(parameterMap.get(false))
                .orElse(Collections.emptyList());

        return new ParameterGroup(onlineParameters, offlineParameters);
    }

    /**
     * 构建最终结果
     *
     * @param namespace         命名空间信息
     * @param onlineParameters        参数列表
     * @param offlineParameterDOs 下线参数列表
     * @param conditionDOs      条件映射
     * @return 配置结果
     */
    protected abstract T buildResult(NamespaceIdNameRecord namespace,
                                     List<Parameter> onlineParameters,
                                     List<OParameterVersionDO> offlineParameterDOs,
                                     Collection<OConditionVersionDO> conditionDOs);

    /**
     * 获取需要查询的版本记录状态列表（灰度的需要查询 INIT 和 RELEASED，正式的需要查询 RELEASED）
     *
     * @return 状态列表
     */
    protected abstract List<VersionStatus> getVersionStatuses();

    /**
     * 构建条件映射
     *
     * @param onlineParameters 在线参数列表
     * @return 条件ID到条件版本的映射
     */
    private Map<String, OConditionVersionDO> buildConditionMap(List<OParameterVersionDO> onlineParameters) {
        if (CollectionUtils.isEmpty(onlineParameters)) {
            return Collections.emptyMap();
        }

        List<String> conditionIds = extractConditionIds(onlineParameters);
        if (CollectionUtils.isEmpty(conditionIds)) {
            return Collections.emptyMap();
        }

        return queryConditionVersions(conditionIds);
    }

    /**
     * 构建参数列表
     *
     * @param onlineParameters 在线参数列表
     * @param conditionMap     条件映射
     * @return 参数列表
     */
    private List<Parameter> buildParameters(List<OParameterVersionDO> onlineParameters,
                                            Map<String, OConditionVersionDO> conditionMap) {
        if (CollectionUtils.isEmpty(onlineParameters)) {
            return Collections.emptyList();
        }

        // 提取参数ID
        List<String> parameterIds = onlineParameters.stream()
                .map(OParameterVersionDO::getParameterId)
                .distinct()
                .collect(Collectors.toList());

        // 获取参数条件映射
        var paramId2paramCondition = getExistParameterConditions(parameterIds);

        // 构建并过滤参数列表
        return onlineParameters.stream()
                .map(p -> buildParameter(p, paramId2paramCondition.getOrDefault(p.getParameterId(), Collections.emptyList())))
                .filter(Objects::nonNull)
                // 客户端希望按照 key 排序，加速检索
                .sorted(Comparator.comparing(Parameter::getKey))
                .collect(Collectors.toList());
    }

    /**
     * 获取参数条件映射
     *
     * @param parameterIds 参数ID列表
     * @return 参数ID到条件列表的映射
     */
    private Map<String, List<OParameterConditionVersionDO>> getExistParameterConditions(List<String> parameterIds) {
        if (CollectionUtils.isEmpty(parameterIds)) {
            return Collections.emptyMap();
        }

        // 查询参数条件版本
        List<OParameterConditionVersionDO> list = parameterConditionVersionDAO.lambdaQuery()
                .in(OParameterConditionVersionDO::getParameterId, parameterIds)
                .in(OParameterConditionVersionDO::getStatus, getVersionStatuses())
                .ne(OParameterConditionVersionDO::getChangeType, ChangeType.DELETE)
                .list();

        // 按参数ID分组
        return list.stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
    }

    /**
     * 提取条件ID列表
     *
     * @param onlineParameters 在线参数列表
     * @return 条件ID列表
     */
    private List<String> extractConditionIds(List<OParameterVersionDO> onlineParameters) {
        if (CollectionUtils.isEmpty(onlineParameters)) {
            return Collections.emptyList();
        }

        return onlineParameters.stream()
                .filter(p -> StringUtils.isNotBlank(p.getConditionsOrder()))
                .flatMap(p -> Arrays.stream(p.getConditionsOrder().split(CONDITION_SEPARATOR)))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询条件版本映射
     *
     * @param conditionIds 条件ID列表
     * @return 条件ID到条件版本的映射
     */
    private Map<String, OConditionVersionDO> queryConditionVersions(List<String> conditionIds) {
        return getConditionVersions(conditionIds, getVersionStatuses());
    }

    /**
     * 获取条件版本映射
     *
     * @param conditionIds    条件ID列表
     * @param allowedStatuses 允许的状态列表
     * @return 条件ID到条件版本的映射
     */
    private Map<String, OConditionVersionDO> getConditionVersions(List<String> conditionIds,
                                                                  List<VersionStatus> allowedStatuses) {
        if (CollectionUtils.isEmpty(conditionIds) || CollectionUtils.isEmpty(allowedStatuses)) {
            return Collections.emptyMap();
        }

        // 查询条件版本
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .in(OConditionVersionDO::getConditionId, conditionIds)
                .in(OConditionVersionDO::getStatus, allowedStatuses)
                .ne(OConditionVersionDO::getChangeType, ChangeType.DELETE)
                .orderByAsc(OConditionVersionDO::getConditionId)
                .list();

        // 转换为映射并处理冲突（优先使用INIT状态的版本）
        return conditions.stream()
                .collect(Collectors.toMap(
                        OConditionVersionDO::getConditionId,
                        Function.identity(),
                        // 有冲突只有可能是一个是 INIT 一个是 RELEASED，优先用 INIT
                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2));
    }

    /**
     * 构建参数对象
     *
     * @param parameter         参数版本
     * @param conditionVersions 参数条件版本列表
     * @return 参数配置
     */
    protected Parameter buildParameter(OParameterVersionDO parameter,
                                       List<OParameterConditionVersionDO> conditionVersions) {
        if (parameter == null || CollectionUtils.isEmpty(conditionVersions)) {
            return null;
        }

        // 构建条件值映射（条件ID -> 参数条件版本）
        Map<String, OParameterConditionVersionDO> conditionIdToValue = buildConditionValueMap(conditionVersions);

        // 检查默认条件是否存在
        if (!conditionIdToValue.containsKey(DEFAULT_CONDITION_ID)) {
            throw CommonException.getDynamicException(ExceptionEnum.PARAM_INVALID, "参数【" + parameter.getParameterKey() + "】缺少默认条件");
        }

        // 构建参数条件值列表
        List<ConditionalValue> conditionalValues = buildConditionalValues(parameter.getConditionsOrder(), conditionIdToValue, parameter.getValueType());

        // 构建参数
        return Parameter.builder()
                .key(parameter.getParameterKey())
                .version(Long.parseLong(parameter.getReleaseVersion()))
                .valueType(parameter.getValueType().getCode().toLowerCase())
                .defaultValue(getValueObj(conditionIdToValue.get(DEFAULT_CONDITION_ID).getValue(), parameter.getValueType()))
                .conditionalValues(conditionalValues)
                .build();
    }

    protected Object getValueObj(String value, ParameterValueType valueType) {
        if (value == null) {
            return null;
        }

        if (valueType.equals(ParameterValueType.BOOLEAN)) {
            return Boolean.parseBoolean(value);
        }
        return value;
    }

    /**
     * 构建条件值映射
     *
     * @param conditionVersions 条件版本列表
     * @return 条件ID到条件值的映射
     */
    private Map<String, OParameterConditionVersionDO> buildConditionValueMap(List<OParameterConditionVersionDO> conditionVersions) {
        return conditionVersions.stream()
                .collect(Collectors.toMap(
                        OParameterConditionVersionDO::getConditionId,
                        Function.identity(),
                        // 优先使用初始状态
                        (v1, v2) -> VersionStatus.INIT.equals(v1.getStatus()) ? v1 : v2
                ));
    }

    /**
     * 构建参数条件值列表
     *
     * @param conditionsOrder    条件顺序字符串
     * @param conditionIdToValue 条件ID到条件值的映射
     * @return 条件值列表
     */
    private List<ConditionalValue> buildConditionalValues(String conditionsOrder,
                                                          Map<String, OParameterConditionVersionDO> conditionIdToValue, ParameterValueType valueType) {
        if (StringUtils.isBlank(conditionsOrder)) {
            return Collections.emptyList();
        }

        return Arrays.stream(conditionsOrder.split(CONDITION_SEPARATOR))
                .filter(conditionIdToValue::containsKey)
                .map(conditionId -> ConditionalValue.builder()
                        .conditionId(conditionId)
                        .value(getValueObj(conditionIdToValue.get(conditionId).getValue(), valueType))
                        .build())
                .collect(Collectors.toList());
    }
}
